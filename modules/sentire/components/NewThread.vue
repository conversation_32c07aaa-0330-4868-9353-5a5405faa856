<script setup lang="ts">
import { ref } from "vue";
import { useCurrentThreadStore, useThreadsStore } from "../stores";
import { useRouter, useRoute } from "vue-router";
import { SquarePenIcon, LoaderCircleIcon } from "lucide-vue-next";

const threadsStore = useThreadsStore();
const currentThreadStore = useCurrentThreadStore();
const router = useRouter();
const route = useRoute();
const pending = ref(false);

const openNewThread = async () => {
  try {
    pending.value = true;
    const thread = await threadsStore.createThread();
    await currentThreadStore.initializeWithThreadId(thread.id);

    router.replace({
      path: route.path,
      query: {
        ...route.query,
        threadId: thread.id,
      },
    });
  } catch (error) {
    console.error(error);
  } finally {
    pending.value = false;
  }
};
</script>

<template>
  <Button
    class="cursor-pointer text-gray-600"
    variant="ghost"
    size="icon"
    :disabled="pending"
    @click="openNewThread"
  >
    <SquarePenIcon v-if="!pending" :size="18" />
    <LoaderCircleIcon v-else :size="18" class="animate-spin" />
  </Button>
</template>
