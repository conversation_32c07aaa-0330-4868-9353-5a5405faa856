<script setup lang="ts">
import type { AssistantMessage, ToolCallMessage } from "~/types/message";
import MiniCard from "./MiniCard.vue";
import type { Query } from "@cubejs-client/core";
import HiddenToolCall from "~/components/HiddenToolCall.vue";
import { useMonitorEditStore } from "../stores/monitorEditStore";

const { message } = defineProps<{
  message: AssistantMessage;
}>();
const monitorEditStore = useMonitorEditStore();
const { debugOpened, timeRange } = storeToRefs(monitorEditStore);

const isToolCall = (content: ToolCallMessage) => {
  return content.type === "tool-call";
};
const isUpdateMonitorMiniCardTool = (content: ToolCallMessage) => {
  return (
    content.type === "tool-call" &&
    content.toolName === "updateMonitorMiniCardTool"
  );
};
const isUpdateMonitorBoardTool = (content: ToolCallMessage) => {
  return (
    content.type === "tool-call" &&
    content.toolName === "updateMonitorBoardTool"
  );
};
const extractUpdateMiniCardToolResult = (content: ToolCallMessage) => {
  if (!content.result) {
    return null;
  }
  return {
    title: content.result.title as string,
    singleValue: content.result.singleValue as {
      query: Query;
    },
    timeSeries: content.result.timeSeries as {
      query: Query;
    },
  };
};

onMounted(async () => {
  const { pinwheel } = await import("ldrs");
  pinwheel.register();
});
</script>

<template>
  <div v-for="(content, contentIndex) in message.content" :key="contentIndex">
    <div v-if="content.type === 'text'">
      <Markdown :source="content.text" class="prose-sm" />
    </div>
    <div v-if="isToolCall(content as ToolCallMessage)" class="my-2">
      <div
        v-if="isUpdateMonitorMiniCardTool(content as ToolCallMessage)"
        class="w-96"
      >
        <ClientOnly>
          <div
            v-if="debugOpened"
            class="text-xs border rounded-lg p-2 max-h-64 overflow-auto"
          >
            <pre>{{
              JSON.stringify(
                extractUpdateMiniCardToolResult(content as ToolCallMessage),
                null,
                2
              )
            }}</pre>
          </div>
          <div v-else>
            <MiniCard
              v-if="extractUpdateMiniCardToolResult(content as ToolCallMessage)"
              :config="extractUpdateMiniCardToolResult(content as ToolCallMessage)"
              :time-range="timeRange"
              :on-click="() => {}"
            />
            <div
              v-else
              class="rounded-lg border shadow w-96 h-64 flex items-center justify-center"
            >
              <l-pinwheel size="40" speed="1" color="oklch(0.5613 0.0924 238.72)" />
            </div>
          </div>
        </ClientOnly>
      </div>
      <HiddenToolCall
        v-else-if="isUpdateMonitorBoardTool(content as ToolCallMessage)"
        :name="(content as ToolCallMessage).toolName"
        :input="(content as ToolCallMessage).args"
        :output="(content as ToolCallMessage).result"
      />

      <StandardToolCall
        v-else
        :name="(content as ToolCallMessage).toolName"
        :input="(content as ToolCallMessage).args"
        :output="(content as ToolCallMessage).result"
      />
    </div>
  </div>
</template>
