<script setup lang="ts">
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { useQueryData } from "../hooks/chart/useQueryData";
import { useSingleValueOption } from "../hooks/chart/useSingleValueOption";
import SparklineChart from "./Sparkline.vue";
import type { MiniCardConfig } from "../types";
import { MonitorIcon } from "lucide-vue-next";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { getGranularity } from "../helpers/timeSelect";

const props = defineProps<{
  config: MiniCardConfig | null;
  timeRange: string;
  onClick: () => void;
}>();

const emits = defineEmits<{
  (e: "click"): void;
}>();

const singleValueQuery = computed(() => {
  const query = props.config?.singleValue?.query ?? null;
  if (query) {
    if (query.timeDimensions && !query.timeDimensions[0].dimension) {
      const tableName = (query.dimensions?.[0] ?? query.measures?.[0])?.split(
        "."
      )[0];
      return {
        ...query,
        timeDimensions: [
          {
            ...query.timeDimensions[0],
            dimension: `${tableName}.timestamp`,
            dateRange: props.timeRange,
          },
        ],
      };
    }
    return query;
  }
  return null;
});
const { resultSet: singleValueResultSet } = useQueryData(singleValueQuery);
const singleValueOption = useSingleValueOption(singleValueResultSet);

const timeSeriesQuery = computed(() => {
  const query = props.config?.timeSeries?.query ?? null;
  if (query && query.timeDimensions) {
    return {
      ...query,
      timeDimensions: [
        {
          ...query.timeDimensions[0],
          dateRange: props.timeRange,
          granularity: getGranularity(props.timeRange),
        },
      ],
    };
  }
  return null;
});

onMounted(async () => {
  const { pinwheel } = await import("ldrs");
  pinwheel.register();
});
</script>

<template>
  <Card
    :class="[
      'group relative h-64 cursor-pointer hover:shadow-lg hover:border-theme-color transition-all duration-200 flex flex-col',
    ]"
    @click="emits('click')"
  >
    <CardHeader>
      <CardTitle class="text-xl flex items-center gap-2">
        <div class="flex gap-4 items-center w-full">
          <div class="p-2 rounded-lg bg-primary/15">
            <MonitorIcon :size="30" class="text-primary" />
          </div>
          <div class="flex flex-col flex-1 min-w-0">
            <!-- TODO set right width for title -->
            <Tooltip>
              <TooltipTrigger>
                <div
                  class="whitespace-nowrap overflow-hidden text-ellipsis w-full max-w-[280px] text-left"
                >
                  {{ props.config?.title }}
                </div>
              </TooltipTrigger>
              <TooltipContent side="top" align="start">
                <p>{{ props.config?.title }}</p>
              </TooltipContent>
            </Tooltip>
            <Badge variant="secondary">
              <span>
                {{ singleValueOption.title }}
              </span>
              <span class="font-semibold">
                {{ singleValueOption.value }}
              </span>
              <span>
                {{ singleValueOption.unit }}
              </span>
            </Badge>
          </div>
          <div class="absolute top-3 right-3">
            <slot name="actions" />
          </div>
        </div>
      </CardTitle>
    </CardHeader>
    <CardContent class="flex-1 flex flex-col p-4 items-center">
      <div class="flex-1 min-h-0 mt-5 w-full">
        <SparklineChart
          :query="timeSeriesQuery"
          height="60px"
          width="100%"
          :time-range="timeRange"
        />
      </div>
    </CardContent>
  </Card>
</template>
