<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";

// 示例 dashboard 配置数据
const sampleDashboardConfig = {
  name: "System Monitoring Dashboard",
  description: "Real-time system performance monitoring",
  rows: [
    {
      title: "CPU & Memory Metrics",
      layout: [
        { i: "cpu-usage", x: 0, y: 0, w: 6, h: 4 },
        { i: "memory-usage", x: 6, y: 0, w: 6, h: 4 },
      ],
    },
    {
      title: "Network & Storage",
      layout: [
        { i: "network-io", x: 0, y: 0, w: 4, h: 3 },
        { i: "disk-usage", x: 4, y: 0, w: 4, h: 3 },
        { i: "storage-io", x: 8, y: 0, w: 4, h: 3 },
      ],
    },
    {
      title: "Application Performance",
      layout: [
        { i: "response-time", x: 0, y: 0, w: 6, h: 4 },
        { i: "error-rate", x: 6, y: 0, w: 6, h: 4 },
      ],
    },
    {
      title: "Database Metrics",
      layout: [
        { i: "db-connections", x: 0, y: 0, w: 3, h: 3 },
        { i: "query-performance", x: 3, y: 0, w: 6, h: 3 },
        { i: "cache-hit-rate", x: 9, y: 0, w: 3, h: 3 },
      ],
    },
  ],
  panels: {
    "cpu-usage": {
      id: "cpu-usage",
      title: "CPU Usage",
      chart: {
        type: "line",
        query: {
          measures: ["system.cpu_usage"],
          timeDimensions: [
            {
              dimension: "timestamp",
              granularity: "minute",
            },
          ],
        },
      },
      tips: "Monitor CPU utilization across all cores",
    },
    "memory-usage": {
      id: "memory-usage",
      title: "Memory Usage",
      chart: {
        type: "stacked-bar",
        query: {
          measures: ["system.memory_used", "system.memory_free"],
          timeDimensions: [
            {
              dimension: "timestamp",
              granularity: "minute",
            },
          ],
        },
      },
      tips: "Track memory consumption patterns",
    },
    "network-io": {
      id: "network-io",
      title: "Network I/O",
      chart: {
        type: "multi-axis-line",
        query: {
          measures: ["network.bytes_in", "network.bytes_out"],
          timeDimensions: [
            {
              dimension: "timestamp",
              granularity: "minute",
            },
          ],
        },
      },
      tips: "Monitor network traffic patterns",
    },
    "disk-usage": {
      id: "disk-usage",
      title: "Disk Usage",
      chart: {
        type: "single-value",
        query: {
          measures: ["storage.disk_usage_percent"],
          timeDimensions: [
            {
              dimension: "timestamp",
              granularity: "hour",
            },
          ],
        },
      },
      tips: "Track disk space utilization",
    },
    "storage-io": {
      id: "storage-io",
      title: "Storage I/O",
      chart: {
        type: "multi-axis-line",
        query: {
          measures: ["storage.read_ops", "storage.write_ops"],
          timeDimensions: [
            {
              dimension: "timestamp",
              granularity: "minute",
            },
          ],
        },
      },
      tips: "Monitor storage read/write operations",
    },
    "response-time": {
      id: "response-time",
      title: "Response Time",
      chart: {
        type: "line",
        query: {
          measures: [
            "application.response_time_avg",
            "application.response_time_p95",
          ],
          timeDimensions: [
            {
              dimension: "timestamp",
              granularity: "minute",
            },
          ],
        },
      },
      tips: "Track application response times",
    },
    "error-rate": {
      id: "error-rate",
      title: "Error Rate",
      chart: {
        type: "stacked-bar",
        query: {
          measures: ["application.error_count", "application.request_count"],
          timeDimensions: [
            {
              dimension: "timestamp",
              granularity: "minute",
            },
          ],
        },
      },
      tips: "Monitor application error rates",
    },
    "db-connections": {
      id: "db-connections",
      title: "Database Connections",
      chart: {
        type: "single-value",
        query: {
          measures: ["database.active_connections"],
          timeDimensions: [
            {
              dimension: "timestamp",
              granularity: "minute",
            },
          ],
        },
      },
      tips: "Monitor active database connections",
    },
    "query-performance": {
      id: "query-performance",
      title: "Query Performance",
      chart: {
        type: "line",
        query: {
          measures: ["database.query_time_avg", "database.slow_query_count"],
          timeDimensions: [
            {
              dimension: "timestamp",
              granularity: "minute",
            },
          ],
        },
      },
      tips: "Track database query performance metrics",
    },
    "cache-hit-rate": {
      id: "cache-hit-rate",
      title: "Cache Hit Rate",
      chart: {
        type: "four-grid",
        query: {
          measures: ["cache.hit_rate", "cache.miss_rate"],
          timeDimensions: [
            {
              dimension: "timestamp",
              granularity: "minute",
            },
          ],
        },
      },
      tips: "Monitor cache efficiency",
    },
  },
  refreshInterval: "30s",
  timeRange: "last_24h",
  theme: {
    primaryColor: "#3b82f6",
    backgroundColor: "#f8fafc",
    textColor: "#1e293b",
  },
  alerts: [
    {
      name: "High CPU Usage",
      condition: "cpu_usage > 80",
      severity: "warning",
    },
    {
      name: "Memory Critical",
      condition: "memory_usage > 90",
      severity: "critical",
    },
    {
      name: "High Disk Usage",
      condition: "disk_usage > 85",
      severity: "warning",
    },
    {
      name: "Network Anomaly",
      condition: "network_bytes_in > 1000000000",
      severity: "info",
    },
    {
      name: "Database Connection Pool Full",
      condition: "db_connections > 95",
      severity: "critical",
    },
    {
      name: "High Response Time",
      condition: "response_time_avg > 2000",
      severity: "warning",
    },
  ],
  notifications: {
    email: {
      enabled: true,
      recipients: ["<EMAIL>", "<EMAIL>"],
      threshold: "warning",
    },
    slack: {
      enabled: true,
      webhook: "https://hooks.slack.com/services/...",
      channel: "#monitoring",
      threshold: "critical",
    },
    sms: {
      enabled: false,
      threshold: "critical",
    },
  },
  dataRetention: {
    realtime: "1h",
    minute: "7d",
    hour: "30d",
    day: "1y",
  },
  exportSettings: {
    formats: ["json", "csv", "pdf"],
    scheduling: {
      daily: true,
      weekly: true,
      monthly: false,
    },
    recipients: ["<EMAIL>"],
  },
  integrations: {
    prometheus: {
      enabled: true,
      endpoint: "http://prometheus:9090",
      scrapeInterval: "15s",
    },
    grafana: {
      enabled: true,
      url: "http://grafana:3000",
      apiKey: "***",
    },
    elasticsearch: {
      enabled: true,
      hosts: ["http://elasticsearch:9200"],
      index: "monitoring-logs",
    },
  },
  customMetrics: [
    {
      name: "business_transactions_per_minute",
      query:
        "SELECT COUNT(*) FROM transactions WHERE timestamp >= NOW() - INTERVAL 1 MINUTE",
      type: "gauge",
      unit: "count/min",
    },
    {
      name: "active_user_sessions",
      query:
        "SELECT COUNT(DISTINCT user_id) FROM sessions WHERE last_activity >= NOW() - INTERVAL 5 MINUTE",
      type: "gauge",
      unit: "users",
    },
    {
      name: "revenue_per_hour",
      query:
        "SELECT SUM(amount) FROM orders WHERE created_at >= NOW() - INTERVAL 1 HOUR",
      type: "counter",
      unit: "currency",
    },
  ],
  dashboardSettings: {
    autoRefresh: true,
    refreshInterval: 30,
    timezone: "UTC",
    dateFormat: "YYYY-MM-DD HH:mm:ss",
    theme: "dark",
    layout: {
      gridSize: 12,
      rowHeight: 80,
      margin: [10, 10],
      containerPadding: [20, 20],
    },
  },
};

// 将配置转换为格式化的JSON字符串
const configText = JSON.stringify(sampleDashboardConfig, null, 2);
const scrollContainer = ref<HTMLElement>();
const scrollContent = ref<HTMLElement>();
const scrollContentClone = ref<HTMLElement>();
const animationId = ref<number>();

// 无限循环滚动动画
const startScrollAnimation = () => {
  if (
    !scrollContainer.value ||
    !scrollContent.value ||
    !scrollContentClone.value
  )
    return;

  const container = scrollContainer.value;
  const content = scrollContent.value;
  const contentClone = scrollContentClone.value;

  const containerHeight = container.clientHeight;
  const contentHeight = content.scrollHeight;

  // 调试信息
  console.log("Infinite scroll animation started:", {
    containerHeight,
    contentHeight,
  });

  // 如果内容不够长，无法滚动
  if (contentHeight <= containerHeight) {
    console.log("Content is not long enough to scroll");
    return;
  }

  // 初始化位置：第一个内容从底部开始，第二个内容紧跟在后面
  let currentY = containerHeight;
  const scrollSpeed = 3.0; // 滚动速度，已加快

  const animate = () => {
    currentY -= scrollSpeed;

    // 当第一个内容完全滚动出顶部时，重置位置
    if (currentY <= -contentHeight) {
      currentY = containerHeight;
    }

    // 第一个内容的位置
    content.style.transform = `translateY(${currentY}px)`;

    // 第二个内容紧跟在第一个内容后面，形成无缝循环
    const cloneY = currentY + contentHeight;
    contentClone.style.transform = `translateY(${cloneY}px)`;

    animationId.value = requestAnimationFrame(animate);
  };

  animationId.value = requestAnimationFrame(animate);
};

onMounted(() => {
  // 延迟启动动画，确保DOM已渲染和内容已加载
  setTimeout(() => {
    startScrollAnimation();
  }, 500);
});

onUnmounted(() => {
  if (animationId.value) {
    cancelAnimationFrame(animationId.value);
  }
});
</script>

<template>
  <div
    class="w-full h-full flex items-center justify-center bg-gradient-to-br from-white to-gray-50"
  >
    <div class="relative w-[500px] h-[400px] max-w-4xl">
      <!-- 滚动容器 -->
      <div
        class="relative w-full bg-white rounded-lg overflow-hidden border border-gray-200 shadow-lg"
        style="height: 400px"
      >
        <!-- 上边界内阴影遮罩 -->
        <div
          class="absolute top-0 left-0 right-0 z-20 pointer-events-none"
          style="
            height: 60px;
            background: linear-gradient(
              180deg,
              rgba(255, 255, 255, 1) 0%,
              rgba(255, 255, 255, 0.9) 30%,
              rgba(255, 255, 255, 0.5) 70%,
              transparent 100%
            );
            box-shadow: inset 0 10px 40px rgba(255, 255, 255, 0.5);
          "
        />

        <!-- 下边界内阴影遮罩 -->
        <div
          class="absolute bottom-0 left-0 right-0 z-20 pointer-events-none"
          style="
            height: 60px;
            background: linear-gradient(
              0deg,
              rgba(255, 255, 255, 1) 0%,
              rgba(255, 255, 255, 0.9) 30%,
              rgba(255, 255, 255, 0.5) 70%,
              transparent 100%
            );
            box-shadow: inset 0 -10px 40px rgba(255, 255, 255, 0.5);
          "
        />

        <!-- 配置代码滚动区域 -->
        <div
          ref="scrollContainer"
          class="absolute inset-0 overflow-hidden"
          style="padding: 60px 24px"
        >
          <!-- 第一个内容实例 -->
          <div
            ref="scrollContent"
            class="transform absolute w-full"
            style="transform: translateY(0px)"
          >
            <pre
              class="text-blue-400 font-mono text-sm leading-relaxed whitespace-pre-wrap"
              >{{ configText }}</pre
            >
          </div>

          <!-- 第二个内容实例（用于无缝循环） -->
          <div
            ref="scrollContentClone"
            class="transform absolute w-full"
            style="transform: translateY(0px)"
          >
            <pre
              class="text-blue-400 font-mono text-sm leading-relaxed whitespace-pre-wrap"
              >{{ configText }}</pre
            >
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 移除了不需要的滚动条样式 */

/* 代码高亮动画 */
pre {
  /* 移除了文字阴影效果 */
}

/* 渐变动画效果 */
@keyframes glow {
  0%,
  100% {
    /* 移除了文字阴影效果 */
  }
  50% {
    /* 移除了文字阴影效果 */
  }
}

pre {
  animation: glow 3s ease-in-out infinite;
}
</style>
