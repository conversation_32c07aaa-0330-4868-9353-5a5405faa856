<script setup lang="ts">
import { useMonitorEditStore } from "../stores/monitorEditStore";
import { GridLayout } from "grid-layout-plus";
import DashboardPanel from "./DashboardPanel.vue";
import PlaceholderPanel from "./PlaceholderPanel.vue";
import { BugIcon } from "lucide-vue-next";
import TimeSelect from "./TimeSelect.vue";
import RefreshChart from "./RefreshChart.vue";
import { usePanels } from "../hooks/usePanels";
import DashboardRow from "./DashboardRow.vue";
import DashboardRenderAnimation from "./DashboardRenderAnimation.vue";

const monitorEditStore = useMonitorEditStore();
const {
  previewDashboardConfig,
  previewBoardOpened,
  debugOpened,
  debugCubeQuery,
  timeRange,
} = storeToRefs(monitorEditStore);

const panels = usePanels(previewDashboardConfig, timeRange);
</script>

<template>
  <div class="p-3 w-full h-full overflow-y-auto">
    <ClientOnly>
      <Teleport to="#page-header-right">
        <div class="flex justify-end gap-2">
          <Button
            v-if="debugCubeQuery"
            class="rounded-full"
            variant="ghost"
            @click="debugOpened = !debugOpened"
          >
            <BugIcon :size="18" />
          </Button>
          <div class="flex items-center gap-2 mr-4">
            <TimeSelect v-model:value="timeRange" />
            <RefreshChart />
          </div>
        </div>
      </Teleport>
    </ClientOnly>

    <div v-if="previewDashboardConfig && previewBoardOpened" class="px-4 py-6">
      <DashboardRow
        v-for="(row, index) in previewDashboardConfig?.rows"
        :key="row.title"
        :title="row.title"
        :color-index="index"
      >
        <GridLayout
          v-model:layout="row.layout"
          :col-num="12"
          :row-height="80"
          :is-draggable="false"
          :is-resizable="false"
          vertical-compact
          use-css-transforms
        >
          <template #item="{ item }">
            <DashboardPanel
              v-if="panels[item.i]"
              :config="panels[item.i]"
              :debug="debugOpened"
              :color-index="index"
              :date-range="timeRange"
            />
            <PlaceholderPanel v-else />
          </template>
        </GridLayout>
      </DashboardRow>
    </div>

    <DashboardRenderAnimation
      v-show="!previewDashboardConfig && previewBoardOpened"
    />
  </div>
</template>
