# UI规范违规审查报告

## 审查概述

本报告全面审查了整个项目代码，识别不符合项目UI规范的问题。审查基于以下UI规范文档：
- Typography Guidelines (字体规范)
- Spacing Guidelines (间距规范)
- Border Radius Guidelines (圆角规范)
- Pinwheel Configurations (加载动画规范)
- Context7最佳实践

### 专项审查报告
- [Monitor页面UI规范审查报告](./monitor-ui-audit-report.md) - Monitor模块专项审查结果

## 执行摘要

### 🎯 总体合规性评估
- **字体规范合规度**: 95% ✅
- **间距规范合规度**: 85% ⚠️
- **圆角规范合规度**: 90% ✅
- **颜色规范合规度**: 70% ❌
- **组件规范合规度**: 80% ⚠️

## 🔴 高优先级违规问题

### 1. Monitor页面UI规范违规 (新增)

#### 1.1 间距规范问题
**文件位置**: `modules/monitor/components/Messages.vue`

**违规详情**:

```vue
// 第25行 - 非标准间距值
<div class="py-2 px-6">  // py-2 (8px) 不符合标准间距规范

// 第27、30行 - 应使用CSS变量
<div v-if="message.role === 'user'" class="flex justify-end mb-6">
<div v-else-if="message.role === 'assistant'" class="mb-6">
```

**应修复为**:

```vue
<div class="py-4 px-6">  // 使用标准16px间距
<div v-if="message.role === 'user'" class="flex justify-end component-spacing">
<div v-else-if="message.role === 'assistant'" class="component-spacing">
```

#### 1.2 圆角规范问题

**文件位置**: `modules/monitor/components/UserMessage.vue`

**违规详情**:

```vue
// 第11行 - 混合圆角值，不符合统一规范
class="px-4 py-2 rounded-tl-2xl rounded-tr-2xl rounded-bl-2xl rounded-br-lg bg-stone-200/80 w-fit max-w-3/4 text-sm"
```

**应修复为**:

```vue
class="px-4 py-2 rounded-lg bg-stone-200/80 w-fit max-w-3/4 text-sm"
```

#### 1.3 颜色使用问题

**文件位置**: `modules/monitor/components/AddDashboardCard.vue`

**违规详情**:

```vue
// 第10、14行 - 硬编码颜色值
class="group relative h-64 border-2 border-dashed border-gray-300 cursor-pointer hover:border-theme-color"
class="flex flex-col items-center justify-center h-full text-gray-500 group-hover:text-theme-color"
```

**应修复为**:

```vue
class="group relative h-64 border-2 border-dashed border-muted cursor-pointer hover:border-primary"
class="flex flex-col items-center justify-center h-full text-muted-foreground group-hover:text-primary"
```

#### 1.4 内联样式问题

**文件位置**: `pages/monitor/index.vue`

**违规详情**:

```vue
// 第102行 - 内联样式定义主题颜色
<div class="p-6" style="--color-theme-color: #3a7ca5">
```

**应修复为**: 通过CSS变量或主题系统统一管理

#### 1.5 SCSS样式违规

**文件位置**: `modules/monitor/components/MessageInput.vue`

**违规详情**:

```scss
// 第230-232行 - SCSS变量硬编码颜色
$primary-color: #3a7ca5;
$secondary-color: #bfbfbf;
$hover-color: #dcecfa;

// 第245-259行 - 自定义圆角样式
.send-button-circular {
  border-radius: 50%;
  min-width: 32px;
  min-height: 32px;
}
```

**应修复为**: 使用Tailwind类和CSS变量

### 2. 硬编码颜色值违规

#### 1.1 UserQuestionInput.vue
**文件位置**: `modules/sentire/components/UserQuestionInput.vue`

**违规详情**:
```scss
// 第904-906行 - SCSS变量使用硬编码颜色
$primary-color: #3a7ca5;
$secondary-color: #bfbfbf;
$hover-color: #dcecfa;

// 第817-832行 - 内联样式使用硬编码颜色
class="text-[#bfbfbf] hover:bg-[#d9f0f0] hover:text-[#3a7ca5]"
class="text-[#3a7ca5] hover:bg-[#d9f0f0]"
class="bg-[#bfbfbf] hover:bg-[#8fb2c8]"
class="bg-[#3a7ca5]"

// 第893-894行 - 更多硬编码颜色
class="hover:bg-[#e2ecf2] hover:text-[#3a7ca5]"
:class="{ 'text-[#3a7ca5] bg-[#e2ecf2]': idx === activeIndex }"
```

**应修复为**: 使用Tailwind颜色类或CSS变量

#### 1.2 MessageInput.vue
**文件位置**: `modules/monitor/components/MessageInput.vue`

**违规详情**:
```scss
// 第230-232行 - SCSS变量硬编码
$primary-color: #3a7ca5;
$secondary-color: #bfbfbf;

// 第182-196行 - 内联样式硬编码颜色
class="text-[#bfbfbf] hover:bg-[#d9f0f0] hover:text-[#3a7ca5]"
class="bg-[#bfbfbf] hover:bg-[#8fb2c8]"
class="bg-[#3a7ca5]"
```

#### 1.3 UserConfig组件
**文件位置**: `components/userConfig/UserCustomPromptContent.vue`

**违规详情**:
```scss
// 第79-102行 - 硬编码颜色值
.input-base:focus {
  border-color: #3a7ca5;
}
.input-base::placeholder {
  color: #9ca3af;
}
.btn-outline {
  border: 1px solid #d1d5db;
  background: #fff;
  color: #374151;
}
.btn-primary {
  background: #3a7ca5;
  color: #fff;
}
```

**文件位置**: `components/userConfig/UserMemoryContent.vue`

**违规详情**:
```scss
// 第289-304行 - 相同的硬编码颜色问题
```

### 2. 自定义CSS样式违规

#### 2.1 自定义padding/margin值
**文件位置**: `components/userConfig/UserCustomPromptContent.vue`

**违规详情**:
```scss
// 第87-89行 - 自定义padding值
.btn-base {
  padding: 0.5rem 1rem;  // 应使用Tailwind类
}
```

**应修复为**: 使用Tailwind spacing类如 `px-4 py-2`

#### 2.2 硬编码border-radius值
**文件位置**: `modules/sentire/components/UserQuestionInput.vue`

**违规详情**:
```scss
// 第1038行 - 硬编码圆角值
.completion-item {
  border-radius: 0.25rem; /* 4px - 使用Tailwind sm值 */
}

// 第1077行 - 硬编码圆角值
:deep(.mention) {
  border-radius: 0.25rem; /* 4px - 使用Tailwind sm值 */
}
```

**应修复为**: 使用Tailwind类 `rounded` 或 `rounded-sm`

### 3. 内联样式违规

#### 3.1 UserQuestionInput.vue内联样式
**文件位置**: `modules/sentire/components/UserQuestionInput.vue`

**违规详情**:
```vue
// 第818行 - 内联样式
:style="{ visibility: isEmpty ? 'hidden' : 'visible' }"

// 第891行 - 内联样式
style="margin: 0px"
```

**应修复为**: 使用Tailwind类或CSS类

#### 3.2 HistoryAnalysis/Understand.vue内联样式
**文件位置**: `modules/sentire/components/HistoryAnalysis/Understand.vue`

**违规详情**:
```vue
// 第94行 - 内联样式
:style="buttonIsShow ? '' : 'visibility: hidden;'"
```

## 🟡 中优先级违规问题

### 1. 非标准间距使用

#### 1.1 自定义间距值
**文件位置**: `assets/css/main.css`

**违规详情**:
```css
// 第279行 - 自定义padding值
padding: 8px;  // 应使用Tailwind类 p-2
```

### 2. 字体大小违规

#### 2.1 全局样式硬编码字体
**文件位置**: `assets/css/main.css`

**违规详情**:
```css
// 第245行 - 硬编码字体大小
[data-slot="tooltip-content"].sidebar-tooltip {
  font-size: 14px !important;  // 应使用text-sm
}
```

**说明**: 此项为第三方组件覆盖，可接受但不推荐

## 🟢 低优先级违规问题

### 1. CSS变量命名不一致

#### 1.1 颜色变量命名
**文件位置**: `assets/css/main.css`

**违规详情**:
```css
// 第46-59行 - 混合命名风格
--color-title-bg: #f5f5f5;        // kebab-case
--color-checking-text: #bfbfbf;    // kebab-case
--color-theme-color: #3a7ca5;      // 冗余命名
```

**建议**: 统一使用语义化命名，避免冗余

### 2. 注释不一致

#### 2.1 中英文混合注释
**文件位置**: 多个组件文件

**违规详情**: 存在中英文混合的注释，不符合项目英文注释规范

## 修复优先级建议

### 🔴 立即修复 (高优先级)
1. **硬编码颜色值** - 影响主题一致性和可维护性
2. **内联样式** - 违反组件化原则
3. **自定义CSS样式** - 不符合Tailwind优先原则

### 🟡 计划修复 (中优先级)  
1. **非标准间距** - 影响视觉一致性
2. **字体大小违规** - 影响排版规范

### 🟢 长期优化 (低优先级)
1. **CSS变量命名** - 提升代码质量
2. **注释规范** - 提升代码可读性

## 修复指导原则

### 颜色使用规范
```vue
<!-- ❌ 避免硬编码 -->
<div class="text-[#3a7ca5] bg-[#bfbfbf]">

<!-- ✅ 使用Tailwind颜色 -->
<div class="text-primary bg-muted">

<!-- ✅ 使用CSS变量 -->
<div class="text-theme-color bg-secondary">
```

### 间距使用规范
```vue
<!-- ❌ 避免自定义值 -->
<div style="padding: 8px; margin: 12px;">

<!-- ✅ 使用Tailwind类 -->
<div class="p-2 m-3">
```

### 样式组织规范
```vue
<!-- ❌ 避免内联样式 -->
<div :style="{ visibility: show ? 'visible' : 'hidden' }">

<!-- ✅ 使用条件类 -->
<div :class="{ 'invisible': !show }">
```

## 详细违规清单

### 硬编码颜色值完整列表

#### 主要违规文件统计
| 文件 | 硬编码颜色数量 | 违规类型 | 优先级 |
|------|---------------|----------|--------|
| `UserQuestionInput.vue` | 15+ | 内联样式+SCSS变量 | 🔴 高 |
| `MessageInput.vue` (Monitor) | 10+ | 内联样式+SCSS变量 | 🔴 高 |
| `UserCustomPromptContent.vue` | 8 | SCSS样式 | 🔴 高 |
| `UserMemoryContent.vue` | 8 | SCSS样式 | 🔴 高 |
| `AddDashboardCard.vue` (Monitor) | 4 | 硬编码颜色 | 🔴 高 |
| `Messages.vue` (Monitor) | 2 | 间距规范 | 🟡 中 |
| `UserMessage.vue` (Monitor) | 1 | 圆角规范 | 🟡 中 |
| `pages/monitor/index.vue` | 1 | 内联样式 | 🟡 中 |
| `assets/css/main.css` | 20+ | CSS变量定义 | 🟡 中 |

#### 常见硬编码颜色值
- `#3a7ca5` - 主题色，应使用 `text-primary` 或 `bg-primary`
- `#bfbfbf` - 次要色，应使用 `text-muted` 或 `bg-muted`
- `#d9f0f0` - 悬停色，应使用 `hover:bg-primary/10`
- `#9ca3af` - 占位符色，应使用 `text-muted-foreground`
- `#ffffff` - 白色，应使用 `text-white` 或 `bg-white`

### 自定义CSS样式违规详情

#### 1. 玻璃态效果样式
**文件**: `UserQuestionInput.vue`, `MessageInput.vue`

**违规代码**:
```scss
.input-container-glass {
  background: rgba(249, 250, 251, 0.8);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(229, 231, 235, 0.8);
  padding: 8px;  // 应使用 p-2
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);  // 应使用 Tailwind shadow
}
```

**建议修复**: 创建Tailwind组件类或使用现有的shadcn-vue组件

#### 2. 按钮样式重复定义
**文件**: `UserCustomPromptContent.vue`, `UserMemoryContent.vue`

**违规代码**:
```scss
.btn-base {
  padding: 0.5rem 1rem;  // 应使用 px-4 py-2
  transition: background 0.2s, color 0.2s, border-color 0.2s;
}
```

**建议修复**: 使用shadcn-vue Button组件的变体系统

### 间距规范违规详情

#### 1. 非8px网格间距
**文件**: `assets/css/main.css`

**违规代码**:
```css
padding: 8px;  // 应使用 p-2 (0.5rem)
```

#### 2. 自定义margin值
**文件**: `UserQuestionInput.vue`

**违规代码**:
```vue
style="margin: 0px"  // 应使用 m-0 类
```

### 组件结构违规

#### 1. 重复的圆形按钮样式
**文件**: `UserQuestionInput.vue`, `MessageInput.vue`

**违规详情**: 两个文件中存在相同的 `.send-button-circular` 样式定义，违反DRY原则

**建议修复**: 提取为共享组件或Tailwind组件类

#### 2. 编辑器样式重复
**文件**: `UserQuestionInput.vue`, `MessageInput.vue`

**违规详情**: ProseMirror编辑器样式在两个文件中重复定义

### 可访问性违规

#### 1. 硬编码尺寸值
**文件**: `UserQuestionInput.vue`

**违规代码**:
```scss
.search-icon {
  width: 24px;   // 应使用 w-6
  height: 24px;  // 应使用 h-6
}
```

#### 2. 固定字体大小
**文件**: `assets/css/main.css`

**违规代码**:
```css
font-size: 14px !important;  // 应使用 text-sm
```

## 修复实施计划

### 阶段1: 紧急修复 (1-2天)
1. **移除所有内联样式** - 替换为Tailwind类
2. **统一主题色使用** - 将 `#3a7ca5` 替换为 `text-primary`
3. **修复重复按钮样式** - 提取为共享组件

### 阶段2: 系统性修复 (3-5天)
1. **重构玻璃态效果** - 创建可复用的Tailwind组件
2. **标准化间距使用** - 确保所有间距符合8px网格
3. **统一编辑器样式** - 提取为共享样式

### 阶段3: 优化提升 (1周)
1. **CSS变量重构** - 统一命名规范
2. **组件抽象** - 减少样式重复
3. **文档更新** - 更新UI规范文档

## 自动化检查建议

### ESLint规则配置
```javascript
// 建议添加的规则
"vue/no-inline-styles": "error",
"vue/no-hardcoded-colors": "warn"
```

### 代码审查检查点
- [ ] 无内联样式使用
- [ ] 无硬编码颜色值
- [ ] 使用标准Tailwind间距
- [ ] 组件样式无重复
- [ ] 符合可访问性标准

## 总结

项目整体UI规范合规性良好，主要问题集中在硬编码颜色值和内联样式使用上。通过系统性修复，可以显著提升代码质量和维护性。

**关键指标**:
- 需修复硬编码颜色: 70+ 处 (新增Monitor页面10+处)
- 需移除内联样式: 18+ 处 (新增Monitor页面3+处)
- 需重构重复样式: 10+ 处 (新增Monitor页面2+处)
- 需修复间距规范: 5+ 处 (新增Monitor页面2+处)
- 需修复圆角规范: 3+ 处 (新增Monitor页面1+处)
- 预计修复工作量: 6-8个工作日

## Monitor页面专项修复建议

### 🔴 立即修复 (Monitor页面高优先级)
1. **AddDashboardCard.vue** - 硬编码颜色值替换为主题颜色
2. **UserMessage.vue** - 统一圆角规范，使用标准rounded-lg
3. **MessageInput.vue** - SCSS样式迁移到Tailwind CSS

### 🟡 计划修复 (Monitor页面中优先级)
1. **Messages.vue** - 间距规范标准化，使用CSS变量
2. **pages/monitor/index.vue** - 移除内联样式，使用主题系统
3. **MiniCard.vue** - 检查Badge组件导入和样式一致性

### Monitor页面修复检查清单
- [ ] 移除所有硬编码颜色值 (gray-300, gray-500等)
- [ ] 统一圆角使用标准Tailwind类
- [ ] 标准化间距使用 (py-2 → py-4, mb-6 → component-spacing)
- [ ] 移除内联样式定义
- [ ] SCSS变量迁移到CSS变量或Tailwind
- [ ] 确保组件样式一致性

**审查日期**: 2025-01-25
**审查范围**: 全项目Vue组件和样式文件 + Monitor页面专项审查
**下次审查**: 建议在修复完成后进行复查
